<?php
/**
 * View Research Session
 */

$pageTitle = $data['page_title'] ?? 'Research Session';
$session = $data['session'] ?? [];

if (empty($session)) {
    echo '<div class="container mx-auto px-4 py-8"><div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">Research session not found.</div></div>';
    return;
}

$links = $session['links'] ?? [];
$notes = $session['notes'] ?? [];
$plans = $session['plans'] ?? [];
?>

<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-start mb-6">
        <div class="flex-1">
            <div class="flex items-center space-x-3 mb-2">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($session['title']) ?></h1>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <?php
                    switch($session['status']) {
                        case 'active': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                        case 'completed': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                        case 'paused': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                        default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                    }
                    ?>">
                    <?= ucfirst($session['status']) ?>
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    <?php
                    switch($session['priority']) {
                        case 'urgent': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                        case 'high': echo 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'; break;
                        case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                        case 'low': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                    }
                    ?>">
                    <?= ucfirst($session['priority']) ?> Priority
                </span>
            </div>
            
            <?php if (!empty($session['description'])): ?>
                <p class="text-gray-600 dark:text-gray-400 mb-3"><?= htmlspecialchars($session['description']) ?></p>
            <?php endif; ?>
            
            <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span><i class="fas fa-calendar mr-1"></i>Created <?= date('M j, Y', strtotime($session['created_at'])) ?></span>
                <span><i class="fas fa-clock mr-1"></i>Updated <?= date('M j, Y g:i A', strtotime($session['updated_at'])) ?></span>
                <span><i class="fas fa-tag mr-1"></i><?= ucfirst(str_replace('_', ' ', $session['research_type'])) ?></span>
            </div>
        </div>
        
        <div class="flex space-x-3">
            <button onclick="startFocusSession()" 
                    class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-brain mr-2"></i>Focus Session
            </button>
            <a href="/momentum/research/session/<?= $session['id'] ?>/create-plan" 
               class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-plus mr-2"></i>Create Plan
            </a>
            <a href="/momentum/research" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back
            </a>
        </div>
    </div>

    <!-- Tags -->
    <?php if (!empty($session['tags'])): ?>
        <div class="mb-6">
            <div class="flex flex-wrap gap-2">
                <?php foreach ($session['tags'] as $tag): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        #<?= htmlspecialchars($tag) ?>
                    </span>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Links Section -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Research Links (<?= count($links) ?>)
                        </h2>
                        <button onclick="showAddLinkModal()" 
                                class="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors">
                            <i class="fas fa-plus mr-1"></i>Add Link
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <?php if (empty($links)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-link text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">No links added yet</p>
                            <button onclick="showAddLinkModal()" 
                                    class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                Add Your First Link
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($links as $link): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-gray-900 dark:text-white">
                                                <a href="/momentum/research/link/access/<?= $link['id'] ?>" 
                                                   target="_blank" 
                                                   class="hover:text-primary-600">
                                                    <?= htmlspecialchars($link['title']) ?>
                                                    <i class="fas fa-external-link-alt text-xs ml-1"></i>
                                                </a>
                                            </h3>
                                            <?php if (!empty($link['description'])): ?>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                    <?= htmlspecialchars($link['description']) ?>
                                                </p>
                                            <?php endif; ?>
                                            <div class="flex items-center mt-2 space-x-4">
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium
                                                    <?php
                                                    switch($link['link_type']) {
                                                        case 'competitor': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                        case 'documentation': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                                                        case 'tool': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                        case 'article': echo 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'; break;
                                                        default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                                                    }
                                                    ?>">
                                                    <?= ucfirst($link['link_type']) ?>
                                                </span>
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium
                                                    <?php
                                                    switch($link['importance']) {
                                                        case 'critical': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                        case 'high': echo 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'; break;
                                                        case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                                                        case 'low': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                    }
                                                    ?>">
                                                    <?= ucfirst($link['importance']) ?>
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= $link['access_count'] ?> accesses
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <select onchange="updateLinkStatus(<?= $link['id'] ?>, this.value)" 
                                                    class="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 dark:bg-gray-700 dark:text-white">
                                                <option value="to_review" <?= $link['status'] === 'to_review' ? 'selected' : '' ?>>To Review</option>
                                                <option value="reviewed" <?= $link['status'] === 'reviewed' ? 'selected' : '' ?>>Reviewed</option>
                                                <option value="implemented" <?= $link['status'] === 'implemented' ? 'selected' : '' ?>>Implemented</option>
                                                <option value="archived" <?= $link['status'] === 'archived' ? 'selected' : '' ?>>Archived</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h2>
                </div>
                <div class="p-6 space-y-3">
                    <button onclick="showAddLinkModal()" 
                            class="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-plus mr-2"></i>Add Link
                    </button>
                    <button onclick="showAddNoteModal()" 
                            class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-sticky-note mr-2"></i>Add Note
                    </button>
                    <a href="/momentum/research/session/<?= $session['id'] ?>/create-plan" 
                       class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-clipboard-list mr-2"></i>Create Plan
                    </a>
                    <a href="/momentum/research/session/<?= $session['id'] ?>/export" 
                       class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-download mr-2"></i>Export Session
                    </a>
                </div>
            </div>

            <!-- Research Plans -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                        Action Plans (<?= count($plans) ?>)
                    </h2>
                </div>
                <div class="p-6">
                    <?php if (empty($plans)): ?>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">No plans created yet</p>
                        <a href="/momentum/research/session/<?= $session['id'] ?>/create-plan" 
                           class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                            Create your first plan →
                        </a>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($plans as $plan): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                                    <h3 class="font-medium text-gray-900 dark:text-white text-sm">
                                        <a href="/momentum/research/plan/<?= $plan['id'] ?>" class="hover:text-primary-600">
                                            <?= htmlspecialchars($plan['title']) ?>
                                        </a>
                                    </h3>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            <?= ucfirst($plan['plan_type']) ?>
                                        </span>
                                        <span class="text-xs font-medium text-primary-600">
                                            <?= $plan['progress'] ?>% complete
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Session Stats -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Session Stats</h2>
                </div>
                <div class="p-6 space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Links:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?= count($links) ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Notes:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?= count($notes) ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Plans:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?= count($plans) ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?= ucfirst($session['status']) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Link Modal -->
<div id="addLinkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Research Link</h3>
            </div>
            <form id="addLinkForm" class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">URL *</label>
                    <input type="url" name="url" required 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                           placeholder="https://example.com">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Title</label>
                    <input type="text" name="title" 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Auto-filled from URL or enter manually">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                    <select name="link_type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                        <option value="article">Article</option>
                        <option value="documentation">Documentation</option>
                        <option value="tool">Tool</option>
                        <option value="competitor">Competitor</option>
                        <option value="reference">Reference</option>
                        <option value="video">Video</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Importance</label>
                    <select name="importance" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideAddLinkModal()" 
                            class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg">
                        Add Link
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showAddLinkModal() {
    document.getElementById('addLinkModal').classList.remove('hidden');
}

function hideAddLinkModal() {
    document.getElementById('addLinkModal').classList.add('hidden');
    document.getElementById('addLinkForm').reset();
}

function updateLinkStatus(linkId, status) {
    fetch(`/momentum/research/link/${linkId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `status=${status}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Optionally show success message
            console.log('Link status updated');
        } else {
            alert('Failed to update link status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating link status');
    });
}

function startFocusSession() {
    const duration = prompt('Focus session duration (minutes):', '25');
    if (duration && !isNaN(duration)) {
        fetch(`/momentum/research/session/<?= $session['id'] ?>/start-focus`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `duration=${duration}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Focus session started for ${duration} minutes!`);
                // You could implement a timer UI here
            } else {
                alert('Failed to start focus session');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error starting focus session');
        });
    }
}

// Add link form submission
document.getElementById('addLinkForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(`/momentum/research/session/<?= $session['id'] ?>/add-link`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            hideAddLinkModal();
            location.reload(); // Refresh to show new link
        } else {
            alert('Failed to add link: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding link');
    });
});

// Close modal when clicking outside
document.getElementById('addLinkModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideAddLinkModal();
    }
});
</script>

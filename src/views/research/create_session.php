<?php
/**
 * Create Research Session View
 */

$pageTitle = $data['page_title'] ?? 'Create Research Session';
?>

<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create Research Session</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Start a new deep research session</p>
        </div>
        <a href="/momentum/research" 
           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>Back to Research
        </a>
    </div>

    <!-- Create Session Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div class="p-6">
            <form method="POST" action="/momentum/research/create-session" class="space-y-6">
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Research Title *
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                           placeholder="e.g., AI Agent Market Analysis, Competitor Research, Technical Investigation">
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                              placeholder="Describe what you're researching and what you hope to discover..."></textarea>
                </div>

                <!-- Research Type -->
                <div>
                    <label for="research_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Research Type
                    </label>
                    <select id="research_type" 
                            name="research_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                        <option value="market_analysis">Market Analysis</option>
                        <option value="technical_research">Technical Research</option>
                        <option value="competitive_analysis">Competitive Analysis</option>
                        <option value="trend_analysis">Trend Analysis</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <!-- Priority -->
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Priority Level
                    </label>
                    <select id="priority" 
                            name="priority"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                        <option value="low">Low Priority</option>
                        <option value="medium" selected>Medium Priority</option>
                        <option value="high">High Priority</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>

                <!-- Tags -->
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tags
                    </label>
                    <input type="text" 
                           id="tags" 
                           name="tags"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                           placeholder="ai, agents, market-research, competition (comma-separated)">
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Add tags to help organize and find your research later
                    </p>
                </div>

                <!-- ADHD-Friendly Options -->
                <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">
                        🧠 ADHD-Friendly Options
                    </h3>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   id="enable_focus_sessions" 
                                   name="enable_focus_sessions" 
                                   checked
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Enable focus session timers (Pomodoro-style)
                            </span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   id="auto_save" 
                                   name="auto_save" 
                                   checked
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Auto-save notes and progress
                            </span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   id="distraction_blocking" 
                                   name="distraction_blocking"
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Enable distraction-blocking mode
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="/momentum/research" 
                       class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Create Research Session
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Start Tips -->
    <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
            💡 Quick Start Tips
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
            <div>
                <h4 class="font-medium mb-2">Research Session Best Practices:</h4>
                <ul class="space-y-1">
                    <li>• Use descriptive titles for easy identification</li>
                    <li>• Choose the right research type for better organization</li>
                    <li>• Add relevant tags for quick filtering</li>
                    <li>• Set appropriate priority levels</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">ADHD-Friendly Features:</h4>
                <ul class="space-y-1">
                    <li>• Focus timers help maintain attention</li>
                    <li>• Auto-save prevents lost work</li>
                    <li>• Distraction blocking improves focus</li>
                    <li>• Visual progress tracking motivates completion</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on title field
    document.getElementById('title').focus();
    
    // Tag input enhancement
    const tagsInput = document.getElementById('tags');
    tagsInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            // Submit form when Enter is pressed in tags field
            this.form.submit();
        }
    });
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        if (!title) {
            e.preventDefault();
            alert('Please enter a research title');
            document.getElementById('title').focus();
            return false;
        }
    });
});
</script>

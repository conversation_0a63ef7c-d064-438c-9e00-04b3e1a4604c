<?php
/**
 * Research Search Results View
 */

$pageTitle = $data['page_title'] ?? 'Search Results';
$query = $data['query'] ?? '';
$type = $data['type'] ?? 'all';
$filters = $data['filters'] ?? [];
$results = $data['results'] ?? [];
?>

<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Search Results</h1>
            <?php if (!empty($query)): ?>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Results for "<strong><?= htmlspecialchars($query) ?></strong>"
                </p>
            <?php endif; ?>
        </div>
        <a href="/momentum/research" 
           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>Back to Research
        </a>
    </div>

    <!-- Search Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-8">
        <div class="p-6">
            <form method="GET" action="/momentum/research/search" class="space-y-4">
                <div class="flex space-x-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="q" 
                               value="<?= htmlspecialchars($query) ?>"
                               placeholder="Search research sessions, links, notes, and plans..."
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <select name="type" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                            <option value="all" <?= $type === 'all' ? 'selected' : '' ?>>All Types</option>
                            <option value="sessions" <?= $type === 'sessions' ? 'selected' : '' ?>>Sessions</option>
                            <option value="links" <?= $type === 'links' ? 'selected' : '' ?>>Links</option>
                            <option value="plans" <?= $type === 'plans' ? 'selected' : '' ?>>Plans</option>
                        </select>
                    </div>
                    <button type="submit" 
                            class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                </div>

                <!-- Advanced Filters -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Research Type</label>
                        <select name="research_type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                            <option value="">All Types</option>
                            <option value="market_analysis" <?= ($filters['research_type'] ?? '') === 'market_analysis' ? 'selected' : '' ?>>Market Analysis</option>
                            <option value="technical_research" <?= ($filters['research_type'] ?? '') === 'technical_research' ? 'selected' : '' ?>>Technical Research</option>
                            <option value="competitive_analysis" <?= ($filters['research_type'] ?? '') === 'competitive_analysis' ? 'selected' : '' ?>>Competitive Analysis</option>
                            <option value="trend_analysis" <?= ($filters['research_type'] ?? '') === 'trend_analysis' ? 'selected' : '' ?>>Trend Analysis</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                            <option value="">All Statuses</option>
                            <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="completed" <?= ($filters['status'] ?? '') === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="paused" <?= ($filters['status'] ?? '') === 'paused' ? 'selected' : '' ?>>Paused</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
                        <select name="priority" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                            <option value="">All Priorities</option>
                            <option value="urgent" <?= ($filters['priority'] ?? '') === 'urgent' ? 'selected' : '' ?>>Urgent</option>
                            <option value="high" <?= ($filters['priority'] ?? '') === 'high' ? 'selected' : '' ?>>High</option>
                            <option value="medium" <?= ($filters['priority'] ?? '') === 'medium' ? 'selected' : '' ?>>Medium</option>
                            <option value="low" <?= ($filters['priority'] ?? '') === 'low' ? 'selected' : '' ?>>Low</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="button" onclick="clearFilters()" 
                                class="w-full bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg font-medium transition-colors">
                            Clear Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Results -->
    <?php if (empty($query)): ?>
        <div class="text-center py-12">
            <i class="fas fa-search text-6xl text-gray-400 mb-4"></i>
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">Search Your Research</h2>
            <p class="text-gray-600 dark:text-gray-400">Enter a search term to find sessions, links, notes, and plans</p>
        </div>
    <?php elseif (empty($results) || (empty($results['sessions'] ?? []) && empty($results['links'] ?? []) && empty($results['plans'] ?? []))): ?>
        <div class="text-center py-12">
            <i class="fas fa-search text-6xl text-gray-400 mb-4"></i>
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-2">No Results Found</h2>
            <p class="text-gray-600 dark:text-gray-400">Try adjusting your search terms or filters</p>
        </div>
    <?php else: ?>
        <div class="space-y-8">
            <!-- Research Sessions Results -->
            <?php if (!empty($results['sessions'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Research Sessions (<?= count($results['sessions']) ?>)
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php foreach ($results['sessions'] as $session): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-gray-900 dark:text-white">
                                                <a href="/momentum/research/session/<?= $session['id'] ?>" class="hover:text-primary-600">
                                                    <?= htmlspecialchars($session['title']) ?>
                                                </a>
                                            </h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                <?= htmlspecialchars(substr($session['description'], 0, 150)) ?>
                                                <?= strlen($session['description']) > 150 ? '...' : '' ?>
                                            </p>
                                            <div class="flex items-center mt-2 space-x-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    <?= ucfirst(str_replace('_', ' ', $session['research_type'])) ?>
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= date('M j, Y', strtotime($session['created_at'])) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php
                                                switch($session['status']) {
                                                    case 'active': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                    case 'completed': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                                                    case 'paused': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                                                    default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
                                                }
                                                ?>">
                                                <?= ucfirst($session['status']) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Research Links Results -->
            <?php if (!empty($results['links'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Research Links (<?= count($results['links']) ?>)
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php foreach ($results['links'] as $link): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-gray-900 dark:text-white">
                                                <a href="/momentum/research/link/access/<?= $link['id'] ?>" 
                                                   target="_blank" 
                                                   class="hover:text-primary-600">
                                                    <?= htmlspecialchars($link['title']) ?>
                                                    <i class="fas fa-external-link-alt text-xs ml-1"></i>
                                                </a>
                                            </h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                <?= htmlspecialchars($link['description']) ?>
                                            </p>
                                            <div class="flex items-center mt-2 space-x-4">
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                    <?= ucfirst($link['link_type']) ?>
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    From: <?= htmlspecialchars($link['session_title'] ?? 'Unknown Session') ?>
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= $link['access_count'] ?> accesses
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php
                                                switch($link['importance']) {
                                                    case 'critical': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                    case 'high': echo 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'; break;
                                                    case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                                                    case 'low': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                }
                                                ?>">
                                                <?= ucfirst($link['importance']) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Research Plans Results -->
            <?php if (!empty($results['plans'])): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Research Plans (<?= count($results['plans']) ?>)
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php foreach ($results['plans'] as $plan): ?>
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <h3 class="font-medium text-gray-900 dark:text-white">
                                                <a href="/momentum/research/plan/<?= $plan['id'] ?>" class="hover:text-primary-600">
                                                    <?= htmlspecialchars($plan['title']) ?>
                                                </a>
                                            </h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                <?= htmlspecialchars(substr($plan['description'], 0, 150)) ?>
                                                <?= strlen($plan['description']) > 150 ? '...' : '' ?>
                                            </p>
                                            <div class="flex items-center mt-2 space-x-4">
                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                    <?= ucfirst($plan['plan_type']) ?>
                                                </span>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    From: <?= htmlspecialchars($plan['session_title'] ?? 'Unknown Session') ?>
                                                </span>
                                                <span class="text-xs font-medium text-primary-600">
                                                    <?= $plan['progress'] ?>% complete
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php
                                                switch($plan['priority']) {
                                                    case 'urgent': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                    case 'high': echo 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'; break;
                                                    case 'medium': echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'; break;
                                                    case 'low': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                }
                                                ?>">
                                                <?= ucfirst($plan['priority']) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<script>
function clearFilters() {
    // Clear all filter fields
    document.querySelector('select[name="research_type"]').value = '';
    document.querySelector('select[name="status"]').value = '';
    document.querySelector('select[name="priority"]').value = '';
    
    // Submit the form to refresh results
    document.querySelector('form').submit();
}
</script>

<?php
/**
 * Research Planning Controller
 *
 * Handles deep search and planning functionality
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/ResearchSession.php';
require_once __DIR__ . '/../models/ResearchLink.php';
require_once __DIR__ . '/../models/ResearchPlan.php';
require_once __DIR__ . '/../models/ResearchNote.php';

class ResearchPlanningController extends BaseController {
    private $researchSessionModel;
    private $researchLinkModel;
    private $researchPlanModel;
    private $researchNoteModel;
    private $db;

    public function __construct() {
        require_once __DIR__ . '/../utils/Database.php';
        $this->db = Database::getInstance();
        $this->researchSessionModel = new ResearchSession();
        $this->researchLinkModel = new ResearchLink();
        $this->researchPlanModel = new ResearchPlan();
        $this->researchNoteModel = new ResearchNote();
    }

    /**
     * Helper methods
     */
    private function requireAuth() {
        $this->requireLogin();
    }

    private function getCurrentUserId() {
        require_once __DIR__ . '/../utils/Session.php';
        $user = Session::getUser();
        return $user['id'] ?? 1; // Default to user ID 1 for testing
    }

    private function setFlashMessage($type, $message) {
        require_once __DIR__ . '/../utils/Session.php';
        Session::setFlash($type, $message);
    }

    private function render($view, $data = []) {
        $this->view($view, $data);
    }

    /**
     * Research dashboard
     */
    public function index() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        // Get recent sessions
        $recentSessions = $this->researchSessionModel->getUserSessions($userId, null, 5);
        
        // Get statistics
        $sessionStats = $this->researchSessionModel->getSessionStats($userId);
        $linkStats = $this->researchLinkModel->getLinkStats($userId);
        $planStats = $this->researchPlanModel->getPlanStats($userId);

        // Get most accessed links
        $popularLinks = $this->researchLinkModel->getMostAccessedLinks($userId, 5);

        // Get recent activity
        $recentActivity = $this->researchSessionModel->getRecentActivity($userId, 7);

        $data = [
            'recent_sessions' => $recentSessions,
            'session_stats' => $sessionStats,
            'link_stats' => $linkStats,
            'plan_stats' => $planStats,
            'popular_links' => $popularLinks,
            'recent_activity' => $recentActivity,
            'page_title' => 'Research & Planning Dashboard'
        ];

        $this->render('research/dashboard', $data);
    }

    /**
     * Create new research session
     */
    public function createSession() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'title' => $_POST['title'] ?? '',
                'description' => $_POST['description'] ?? '',
                'research_type' => $_POST['research_type'] ?? 'other',
                'priority' => $_POST['priority'] ?? 'medium',
                'tags' => !empty($_POST['tags']) ? explode(',', $_POST['tags']) : []
            ];

            $sessionId = $this->researchSessionModel->createSession($userId, $data);

            if ($sessionId) {
                $this->setFlashMessage('success', 'Research session created successfully');
                $this->redirect('/momentum/research/session/' . $sessionId);
            } else {
                $this->setFlashMessage('error', 'Failed to create research session');
            }
        }

        $this->render('research/create_session', [
            'page_title' => 'Create Research Session'
        ]);
    }

    /**
     * View research session
     */
    public function viewSession($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $session = $this->researchSessionModel->getSessionWithDetails($sessionId, $userId);

        if (!$session) {
            $this->setFlashMessage('error', 'Research session not found');
            $this->redirect('/momentum/research');
            return;
        }

        $data = [
            'session' => $session,
            'page_title' => $session['title']
        ];

        $this->render('research/view_session', $data);
    }

    /**
     * Add link to research session
     */
    public function addLink($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'title' => $_POST['title'] ?? '',
                'url' => $_POST['url'] ?? '',
                'description' => $_POST['description'] ?? '',
                'link_type' => $_POST['link_type'] ?? 'other',
                'importance' => $_POST['importance'] ?? 'medium',
                'notes' => $_POST['notes'] ?? '',
                'tags' => !empty($_POST['tags']) ? explode(',', $_POST['tags']) : []
            ];

            // Extract metadata from URL
            if (!empty($data['url'])) {
                $metadata = $this->researchLinkModel->extractMetadata($data['url']);
                $data['metadata'] = $metadata;
                
                // Use extracted title if none provided
                if (empty($data['title']) && !empty($metadata['page_title'])) {
                    $data['title'] = $metadata['page_title'];
                }
            }

            $linkId = $this->researchLinkModel->addLink($sessionId, $userId, $data);

            if ($linkId) {
                $this->jsonResponse(['success' => true, 'link_id' => $linkId]);
            } else {
                $this->jsonResponse(['success' => false, 'error' => 'Failed to add link']);
            }
        }
    }

    /**
     * Update link status
     */
    public function updateLinkStatus($linkId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $status = $_POST['status'] ?? '';
            $notes = $_POST['notes'] ?? '';

            $success = $this->researchLinkModel->updateStatus($linkId, $userId, $status, $notes);

            $this->jsonResponse(['success' => $success]);
        }
    }

    /**
     * Record link access
     */
    public function accessLink($linkId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $this->researchLinkModel->recordAccess($linkId, $userId);
        
        // Get the link URL and redirect
        $link = $this->researchLinkModel->getById($linkId);
        if ($link && $link['user_id'] == $userId) {
            $this->redirect($link['url']);
        } else {
            $this->setFlashMessage('error', 'Link not found');
            $this->redirect('/momentum/research');
        }
    }

    /**
     * Create plan from research
     */
    public function createPlan($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'title' => $_POST['title'] ?? '',
                'description' => $_POST['description'] ?? '',
                'plan_type' => $_POST['plan_type'] ?? 'implementation',
                'priority' => $_POST['priority'] ?? 'medium',
                'estimated_effort' => $_POST['estimated_effort'] ?? 'medium',
                'estimated_duration_days' => !empty($_POST['estimated_duration_days']) ? (int)$_POST['estimated_duration_days'] : null,
                'success_criteria' => $_POST['success_criteria'] ?? '',
                'risks_and_mitigation' => $_POST['risks_and_mitigation'] ?? '',
                'resources_needed' => $_POST['resources_needed'] ?? '',
                'tags' => !empty($_POST['tags']) ? explode(',', $_POST['tags']) : []
            ];

            $planId = $this->researchPlanModel->createPlan($sessionId, $userId, $data);

            if ($planId) {
                // Generate default action items
                $this->researchPlanModel->generateActionItems($planId, $userId);
                
                $this->setFlashMessage('success', 'Research plan created successfully');
                $this->redirect('/momentum/research/plan/' . $planId);
            } else {
                $this->setFlashMessage('error', 'Failed to create research plan');
            }
        }

        // Get session info for form
        $session = $this->researchSessionModel->getById($sessionId);
        
        $this->render('research/create_plan', [
            'session' => $session,
            'page_title' => 'Create Research Plan'
        ]);
    }

    /**
     * View research plan
     */
    public function viewPlan($planId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $plan = $this->researchPlanModel->getPlanWithDetails($planId, $userId);

        if (!$plan) {
            $this->setFlashMessage('error', 'Research plan not found');
            $this->redirect('/momentum/research');
            return;
        }

        $data = [
            'plan' => $plan,
            'page_title' => $plan['title']
        ];

        $this->render('research/view_plan', $data);
    }

    /**
     * Convert plan to project
     */
    public function convertPlanToProject($planId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $projectData = [
                'name' => $_POST['project_name'] ?? '',
                'description' => $_POST['project_description'] ?? '',
                'start_date' => $_POST['start_date'] ?? date('Y-m-d'),
                'end_date' => $_POST['end_date'] ?? null,
                'conversion_type' => $_POST['conversion_type'] ?? 'full_project',
                'conversion_notes' => $_POST['conversion_notes'] ?? ''
            ];

            $projectId = $this->researchPlanModel->convertToProject($planId, $userId, $projectData);

            if ($projectId) {
                $this->setFlashMessage('success', 'Plan converted to project successfully');
                $this->redirect('/momentum/projects/view/' . $projectId);
            } else {
                $this->setFlashMessage('error', 'Failed to convert plan to project');
            }
        }

        $plan = $this->researchPlanModel->getPlanWithDetails($planId, $userId);
        
        $this->render('research/convert_to_project', [
            'plan' => $plan,
            'page_title' => 'Convert Plan to Project'
        ]);
    }

    /**
     * Search functionality
     */
    public function search() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $query = $_GET['q'] ?? '';
        $type = $_GET['type'] ?? 'all';
        $filters = [
            'research_type' => $_GET['research_type'] ?? '',
            'status' => $_GET['status'] ?? '',
            'priority' => $_GET['priority'] ?? '',
            'link_type' => $_GET['link_type'] ?? '',
            'plan_type' => $_GET['plan_type'] ?? ''
        ];

        $results = [];

        if ($type === 'all' || $type === 'sessions') {
            $results['sessions'] = $this->researchSessionModel->searchSessions($userId, $query, $filters);
        }

        if ($type === 'all' || $type === 'links') {
            $results['links'] = $this->researchLinkModel->searchLinks($userId, $query, $filters);
        }

        if ($type === 'all' || $type === 'plans') {
            $results['plans'] = $this->researchPlanModel->searchPlans($userId, $query, $filters);
        }

        $data = [
            'query' => $query,
            'type' => $type,
            'filters' => $filters,
            'results' => $results,
            'page_title' => 'Search Results'
        ];

        $this->render('research/search_results', $data);
    }

    /**
     * ADHD-friendly focus session
     */
    public function startFocusSession($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $duration = (int)($_POST['duration'] ?? 25); // Default 25 minutes

            // Create focus session record
            $focusData = [
                'research_session_id' => $sessionId,
                'user_id' => $userId,
                'start_time' => date('Y-m-d H:i:s'),
                'planned_duration_minutes' => $duration,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $sql = "INSERT INTO research_focus_sessions 
                    (research_session_id, user_id, start_time, planned_duration_minutes, created_at) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $focusSessionId = $this->db->insert($sql, array_values($focusData));

            $this->jsonResponse([
                'success' => true,
                'focus_session_id' => $focusSessionId,
                'duration' => $duration
            ]);
        }
    }

    /**
     * End focus session
     */
    public function endFocusSession($focusSessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $quality = $_POST['quality'] ?? 'good';
            $distractions = (int)($_POST['distractions'] ?? 0);
            $notes = $_POST['notes'] ?? '';

            $sql = "UPDATE research_focus_sessions 
                    SET end_time = NOW(),
                        actual_duration_minutes = TIMESTAMPDIFF(MINUTE, start_time, NOW()),
                        focus_quality = ?,
                        distractions_count = ?,
                        notes = ?
                    WHERE id = ? AND user_id = ?";

            $success = $this->db->query($sql, [$quality, $distractions, $notes, $focusSessionId, $userId]);

            $this->jsonResponse(['success' => $success]);
        }
    }

    /**
     * Export research session
     */
    public function exportSession($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $session = $this->researchSessionModel->getSessionWithDetails($sessionId, $userId);

        if (!$session) {
            $this->setFlashMessage('error', 'Research session not found');
            $this->redirect('/momentum/research');
            return;
        }

        $format = $_GET['format'] ?? 'json';

        switch ($format) {
            case 'json':
                header('Content-Type: application/json');
                header('Content-Disposition: attachment; filename="research_session_' . $sessionId . '.json"');
                echo json_encode($session, JSON_PRETTY_PRINT);
                break;

            case 'markdown':
                header('Content-Type: text/markdown');
                header('Content-Disposition: attachment; filename="research_session_' . $sessionId . '.md"');
                $this->render('research/export_markdown', ['session' => $session], false);
                break;

            default:
                $this->setFlashMessage('error', 'Invalid export format');
                $this->redirect('/momentum/research/session/' . $sessionId);
        }
    }

    /**
     * List all research sessions
     */
    public function listSessions() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $sessions = $this->researchSessionModel->getUserSessions($userId);

        $data = [
            'sessions' => $sessions,
            'page_title' => 'All Research Sessions'
        ];

        $this->render('research/list_sessions', $data);
    }

    /**
     * List all research links
     */
    public function listLinks() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $links = $this->researchLinkModel->searchLinks($userId, '');

        $data = [
            'links' => $links,
            'page_title' => 'All Research Links'
        ];

        $this->render('research/list_links', $data);
    }

    /**
     * List all research plans
     */
    public function listPlans() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $plans = $this->researchPlanModel->searchPlans($userId, '');

        $data = [
            'plans' => $plans,
            'page_title' => 'All Research Plans'
        ];

        $this->render('research/list_plans', $data);
    }

    /**
     * Research templates
     */
    public function templates() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        // For now, show a simple templates page
        $data = [
            'page_title' => 'Research Templates',
            'templates' => [] // Will be implemented later
        ];

        $this->render('research/templates', $data);
    }

    /**
     * Knowledge base
     */
    public function knowledgeBase() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        // For now, show a simple knowledge base page
        $data = [
            'page_title' => 'Knowledge Base',
            'entries' => [] // Will be implemented later
        ];

        $this->render('research/knowledge_base', $data);
    }

    /**
     * Get session data (AJAX)
     */
    public function getSessionData($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $session = $this->researchSessionModel->getSessionWithDetails($sessionId, $userId);

        if (!$session) {
            $this->jsonResponse(['success' => false, 'error' => 'Session not found']);
            return;
        }

        $this->jsonResponse(['success' => true, 'session' => $session]);
    }

    /**
     * Update session (AJAX)
     */
    public function updateSession($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $data = [
            'title' => $_POST['title'] ?? '',
            'description' => $_POST['description'] ?? '',
            'status' => $_POST['status'] ?? '',
            'priority' => $_POST['priority'] ?? ''
        ];

        $success = $this->researchSessionModel->update($sessionId, $data);

        $this->jsonResponse(['success' => $success]);
    }

    /**
     * Get plan data (AJAX)
     */
    public function getPlanData($planId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $plan = $this->researchPlanModel->getPlanWithDetails($planId, $userId);

        if (!$plan) {
            $this->jsonResponse(['success' => false, 'error' => 'Plan not found']);
            return;
        }

        $this->jsonResponse(['success' => true, 'plan' => $plan]);
    }

    /**
     * Update plan progress (AJAX)
     */
    public function updatePlanProgress($planId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $success = $this->researchPlanModel->updateProgress($planId, $userId);

        $this->jsonResponse(['success' => $success]);
    }

    /**
     * Bulk import links (AJAX)
     */
    public function bulkImportLinks($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $links = json_decode($_POST['links'] ?? '[]', true);

        if (empty($links)) {
            $this->jsonResponse(['success' => false, 'error' => 'No links provided']);
            return;
        }

        $result = $this->researchLinkModel->bulkImportLinks($sessionId, $userId, $links);

        $this->jsonResponse(['success' => true, 'result' => $result]);
    }

    /**
     * Get research statistics (AJAX)
     */
    public function getResearchStats() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $sessionStats = $this->researchSessionModel->getSessionStats($userId);
        $linkStats = $this->researchLinkModel->getLinkStats($userId);
        $planStats = $this->researchPlanModel->getPlanStats($userId);

        $stats = [
            'sessions' => $sessionStats,
            'links' => $linkStats,
            'plans' => $planStats
        ];

        $this->jsonResponse(['success' => true, 'stats' => $stats]);
    }

    /**
     * Auto-save research note (AJAX)
     */
    public function autoSaveNote($sessionId) {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $data = [
            'title' => $_POST['title'] ?? 'Auto-saved Notes',
            'content' => $_POST['content'] ?? '',
            'note_type' => $_POST['note_type'] ?? 'finding'
        ];

        $noteId = $this->researchNoteModel->autoSaveNote($sessionId, $userId, $data);

        $this->jsonResponse(['success' => $noteId !== false, 'note_id' => $noteId]);
    }

    /**
     * Get link preview (AJAX)
     */
    public function getLinkPreview() {
        $url = $_POST['url'] ?? '';

        if (empty($url)) {
            $this->jsonResponse(['success' => false, 'error' => 'No URL provided']);
            return;
        }

        $metadata = $this->researchLinkModel->extractMetadata($url);

        $this->jsonResponse(['success' => true, 'metadata' => $metadata]);
    }

    /**
     * Archive old sessions (AJAX)
     */
    public function archiveOldSessions() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $daysOld = (int)($_POST['days_old'] ?? 90);

        $archived = $this->researchSessionModel->archiveOldSessions($userId, $daysOld);

        $this->jsonResponse(['success' => true, 'archived_count' => $archived]);
    }

    /**
     * Get analytics (AJAX)
     */
    public function getAnalytics() {
        $this->requireAuth();
        $userId = $this->getCurrentUserId();

        $analytics = [
            'recent_activity' => $this->researchSessionModel->getRecentActivity($userId, 30),
            'popular_links' => $this->researchLinkModel->getMostAccessedLinks($userId, 10),
            'session_stats' => $this->researchSessionModel->getSessionStats($userId),
            'link_stats' => $this->researchLinkModel->getLinkStats($userId),
            'plan_stats' => $this->researchPlanModel->getPlanStats($userId)
        ];

        $this->jsonResponse(['success' => true, 'analytics' => $analytics]);
    }

    /**
     * Helper method for JSON responses (override parent)
     */
    protected function jsonResponse($data, $statusCode = 200) {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}

<?php
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    
    echo "Testing direct table access...\n\n";
    
    // Test research_sessions
    try {
        $count = $db->fetchOne('SELECT COUNT(*) as count FROM research_sessions');
        echo "✓ research_sessions table accessible - " . $count['count'] . " records\n";
    } catch (Exception $e) {
        echo "✗ research_sessions error: " . $e->getMessage() . "\n";
    }
    
    // Test research_links
    try {
        $count = $db->fetchOne('SELECT COUNT(*) as count FROM research_links');
        echo "✓ research_links table accessible - " . $count['count'] . " records\n";
    } catch (Exception $e) {
        echo "✗ research_links error: " . $e->getMessage() . "\n";
    }
    
    // Test research_plans
    try {
        $count = $db->fetchOne('SELECT COUNT(*) as count FROM research_plans');
        echo "✓ research_plans table accessible - " . $count['count'] . " records\n";
    } catch (Exception $e) {
        echo "✗ research_plans error: " . $e->getMessage() . "\n";
    }
    
    echo "\nTesting models...\n";
    
    // Test ResearchSession model
    require_once 'src/models/ResearchSession.php';
    $sessionModel = new ResearchSession();
    $sessions = $sessionModel->getUserSessions(1);
    echo "✓ ResearchSession model works - found " . count($sessions) . " sessions\n";
    
    if (!empty($sessions)) {
        echo "Sample session: " . $sessions[0]['title'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>

<?php
/**
 * Test Research Route Access
 */

echo "Testing Research Route Access...\n\n";

// Test 1: Check if we can access the research dashboard
echo "1. Testing research dashboard access...\n";
$url = 'http://localhost/momentum/research';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "✅ Research dashboard accessible (HTTP $httpCode)\n";
} else {
    echo "❌ Research dashboard not accessible (HTTP $httpCode)\n";
}

// Test 2: Check if we can access the create session page
echo "\n2. Testing create session page access...\n";
$url = 'http://localhost/momentum/research/create-session';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "✅ Create session page accessible (HTTP $httpCode)\n";
} else {
    echo "❌ Create session page not accessible (HTTP $httpCode)\n";
    echo "Response preview: " . substr($response, 0, 200) . "...\n";
}

// Test 3: Check if controller is accessible
echo "\n3. Testing controller accessibility...\n";
try {
    require_once 'src/controllers/ResearchPlanningController.php';
    $controller = new ResearchPlanningController();
    echo "✅ ResearchPlanningController instantiated successfully\n";
    
    if (method_exists($controller, 'createSession')) {
        echo "✅ createSession method exists\n";
    } else {
        echo "❌ createSession method missing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Controller error: " . $e->getMessage() . "\n";
}

// Test 4: Check if view files exist
echo "\n4. Testing view files...\n";
$viewFiles = [
    'src/views/research/dashboard.php',
    'src/views/research/create_session.php',
    'src/views/research/view_session.php',
    'src/views/research/search_results.php'
];

foreach ($viewFiles as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists\n";
    } else {
        echo "❌ $file missing\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "ROUTE TEST COMPLETE\n";
echo str_repeat("=", 50) . "\n";
?>

<?php
/**
 * Debug Research Routes
 */

// Simulate the request
$_SERVER['REQUEST_URI'] = '/momentum/research/create-session';
$_SERVER['REQUEST_METHOD'] = 'GET';

echo "Testing research routes...\n";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "\n";
echo "Request Method: " . $_SERVER['REQUEST_METHOD'] . "\n\n";

// Get the path
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

echo "Parsed path: " . $path . "\n\n";

// Test the specific route
if ($path === '/momentum/research/create-session') {
    echo "✅ Route matches: /momentum/research/create-session\n";
} else {
    echo "❌ Route does not match\n";
}

// Test if controller exists
echo "\nTesting controller...\n";
try {
    require_once 'src/controllers/ResearchPlanningController.php';
    $controller = new ResearchPlanningController();
    echo "✅ Controller loaded successfully\n";
    
    // Test if method exists
    if (method_exists($controller, 'createSession')) {
        echo "✅ createSession method exists\n";
    } else {
        echo "❌ createSession method missing\n";
    }
    
} catch (Exception $e) {
    echo "❌ Controller error: " . $e->getMessage() . "\n";
}

// Test if routes file is being included
echo "\nTesting routes inclusion...\n";
$indexContent = file_get_contents('public/index.php');
if (strpos($indexContent, 'research_routes.php') !== false) {
    echo "✅ Research routes included in main index.php\n";
} else {
    echo "❌ Research routes NOT included in main index.php\n";
}

// Test the actual route inclusion
echo "\nTesting route file inclusion...\n";
try {
    // Include the controller first
    require_once 'src/controllers/ResearchPlanningController.php';
    
    // Now test the routes
    ob_start();
    include 'src/routes/research_routes.php';
    $output = ob_get_clean();
    
    echo "✅ Routes file included successfully\n";
    if (!empty($output)) {
        echo "Route output: " . $output . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Routes inclusion error: " . $e->getMessage() . "\n";
}
?>

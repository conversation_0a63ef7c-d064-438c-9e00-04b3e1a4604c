-- Deep Search & Planning System Database Schema

-- Research Sessions table - Track research activities
CREATE TABLE research_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    research_type ENUM('market_analysis', 'technical_research', 'competitive_analysis', 'trend_analysis', 'other') DEFAULT 'other',
    status ENUM('active', 'completed', 'paused', 'archived') DEFAULT 'active',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    tags JSON, -- Array of tags for categorization
    metadata JSON, -- Additional research metadata
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    completed_at DATETIME NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Research Links table - Store important links and resources
CREATE TABLE research_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    research_session_id INT NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    description TEXT,
    link_type ENUM('article', 'documentation', 'tool', 'competitor', 'reference', 'video', 'other') DEFAULT 'other',
    importance ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    status ENUM('to_review', 'reviewed', 'implemented', 'archived') DEFAULT 'to_review',
    notes TEXT,
    tags JSON,
    metadata JSON, -- Store extracted content, screenshots, etc.
    access_count INT DEFAULT 0,
    last_accessed DATETIME NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Research Notes table - Store research findings and insights
CREATE TABLE research_notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    research_session_id INT NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    note_type ENUM('finding', 'insight', 'question', 'action_item', 'summary', 'other') DEFAULT 'finding',
    importance ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    tags JSON,
    linked_links JSON, -- Array of research_link IDs
    metadata JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Research Plans table - Convert research into actionable plans
CREATE TABLE research_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    research_session_id INT NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    plan_type ENUM('implementation', 'strategy', 'roadmap', 'experiment', 'other') DEFAULT 'implementation',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    estimated_effort ENUM('small', 'medium', 'large', 'xl') DEFAULT 'medium',
    estimated_duration_days INT DEFAULT NULL,
    success_criteria TEXT,
    risks_and_mitigation TEXT,
    resources_needed TEXT,
    status ENUM('draft', 'ready', 'in_progress', 'completed', 'cancelled') DEFAULT 'draft',
    progress INT DEFAULT 0, -- Progress percentage (0-100)
    tags JSON,
    metadata JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Plan Action Items table - Break plans into actionable tasks
CREATE TABLE plan_action_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    research_plan_id INT NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    action_type ENUM('research', 'development', 'testing', 'documentation', 'communication', 'other') DEFAULT 'other',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    estimated_time_minutes INT DEFAULT NULL,
    dependencies JSON, -- Array of other action item IDs
    status ENUM('todo', 'in_progress', 'blocked', 'completed', 'cancelled') DEFAULT 'todo',
    assigned_to INT NULL, -- For team collaboration
    due_date DATE NULL,
    notes TEXT,
    completion_notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    completed_at DATETIME NULL,
    FOREIGN KEY (research_plan_id) REFERENCES research_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Research to Project Conversions table - Track when research becomes projects
CREATE TABLE research_project_conversions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    research_session_id INT NOT NULL,
    research_plan_id INT NULL,
    project_id INT NOT NULL,
    user_id INT NOT NULL,
    conversion_type ENUM('full_project', 'partial_implementation', 'experiment', 'prototype') DEFAULT 'full_project',
    conversion_notes TEXT,
    success_metrics TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (research_plan_id) REFERENCES research_plans(id) ON DELETE SET NULL,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Research Templates table - Reusable research frameworks
CREATE TABLE research_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_type ENUM('market_analysis', 'competitive_research', 'technical_evaluation', 'trend_analysis', 'custom') DEFAULT 'custom',
    template_data JSON, -- Structure for research session, questions, etc.
    is_public BOOLEAN DEFAULT FALSE,
    usage_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    tags JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Knowledge Base table - Searchable repository of all research
CREATE TABLE knowledge_base_entries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    content_type ENUM('research_summary', 'best_practice', 'lesson_learned', 'reference', 'template', 'other') DEFAULT 'other',
    source_type ENUM('research_session', 'research_note', 'external', 'experience') DEFAULT 'research_session',
    source_id INT NULL, -- ID of source (research_session, research_note, etc.)
    tags JSON,
    search_keywords TEXT, -- For full-text search optimization
    importance ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    access_count INT DEFAULT 0,
    last_accessed DATETIME NULL,
    is_archived BOOLEAN DEFAULT FALSE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Research Collaboration table - Share research with team members
CREATE TABLE research_collaborations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    research_session_id INT NOT NULL,
    user_id INT NOT NULL, -- User being given access
    shared_by INT NOT NULL, -- User who shared
    permission_level ENUM('view', 'comment', 'edit', 'admin') DEFAULT 'view',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_collaboration (research_session_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ADHD-Specific Features

-- Research Focus Sessions table - Track focused research periods
CREATE TABLE research_focus_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    research_session_id INT NOT NULL,
    user_id INT NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NULL,
    planned_duration_minutes INT DEFAULT 25, -- Default Pomodoro
    actual_duration_minutes INT NULL,
    focus_quality ENUM('poor', 'fair', 'good', 'excellent') NULL,
    distractions_count INT DEFAULT 0,
    notes TEXT,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (research_session_id) REFERENCES research_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Research Bookmarks table - Quick access to important items
CREATE TABLE research_bookmarks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    bookmark_type ENUM('research_session', 'research_link', 'research_note', 'research_plan') NOT NULL,
    bookmark_id INT NOT NULL,
    bookmark_name VARCHAR(255) NOT NULL,
    category VARCHAR(100) DEFAULT 'general',
    notes TEXT,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Indexes for performance
CREATE INDEX idx_research_sessions_user_status ON research_sessions(user_id, status);
CREATE INDEX idx_research_sessions_type ON research_sessions(research_type);
CREATE INDEX idx_research_links_session ON research_links(research_session_id);
CREATE INDEX idx_research_links_status ON research_links(status);
CREATE INDEX idx_research_notes_session ON research_notes(research_session_id);
CREATE INDEX idx_research_plans_session ON research_plans(research_session_id);
CREATE INDEX idx_research_plans_status ON research_plans(status);
CREATE INDEX idx_plan_action_items_plan ON plan_action_items(research_plan_id);
CREATE INDEX idx_plan_action_items_status ON plan_action_items(status);
CREATE INDEX idx_knowledge_base_user ON knowledge_base_entries(user_id);
CREATE INDEX idx_knowledge_base_type ON knowledge_base_entries(content_type);
CREATE INDEX idx_research_focus_sessions_user ON research_focus_sessions(user_id);
CREATE INDEX idx_research_bookmarks_user ON research_bookmarks(user_id);

-- Full-text search indexes
ALTER TABLE research_sessions ADD FULLTEXT(title, description);
ALTER TABLE research_links ADD FULLTEXT(title, description, notes);
ALTER TABLE research_notes ADD FULLTEXT(title, content);
ALTER TABLE research_plans ADD FULLTEXT(title, description);
ALTER TABLE knowledge_base_entries ADD FULLTEXT(title, content, search_keywords);

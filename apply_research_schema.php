<?php
/**
 * Apply Research & Planning Database Schema
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    echo "Connected to database successfully.\n";

    // Read the schema file
    $schemaFile = 'database/research_planning_schema.sql';
    
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }

    $schema = file_get_contents($schemaFile);
    
    if ($schema === false) {
        throw new Exception("Failed to read schema file: $schemaFile");
    }

    echo "Schema file loaded successfully.\n";

    // Split the schema into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $schema)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );

    echo "Found " . count($statements) . " SQL statements to execute.\n\n";

    $successCount = 0;
    $errorCount = 0;

    // Execute each statement
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        
        if (empty($statement)) {
            continue;
        }

        echo "Executing statement " . ($index + 1) . "...\n";
        
        try {
            $result = $db->query($statement);
            
            if ($result !== false) {
                echo "✓ Success\n";
                $successCount++;
            } else {
                echo "✗ Failed (but continuing)\n";
                $errorCount++;
            }
        } catch (Exception $e) {
            echo "✗ Error: " . $e->getMessage() . "\n";
            $errorCount++;
            
            // Continue with other statements even if one fails
            continue;
        }
        
        echo "\n";
    }

    echo "Schema application completed!\n";
    echo "Successful statements: $successCount\n";
    echo "Failed statements: $errorCount\n\n";

    // Verify tables were created
    echo "Verifying table creation...\n";
    
    $tables = [
        'research_sessions',
        'research_links', 
        'research_notes',
        'research_plans',
        'plan_action_items',
        'research_project_conversions',
        'research_templates',
        'knowledge_base_entries',
        'research_collaborations',
        'research_focus_sessions',
        'research_bookmarks'
    ];

    $createdTables = [];
    $missingTables = [];

    foreach ($tables as $table) {
        try {
            $result = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
            
            if ($result) {
                $createdTables[] = $table;
                echo "✓ Table '$table' exists\n";
            } else {
                $missingTables[] = $table;
                echo "✗ Table '$table' missing\n";
            }
        } catch (Exception $e) {
            $missingTables[] = $table;
            echo "✗ Error checking table '$table': " . $e->getMessage() . "\n";
        }
    }

    echo "\nTable verification summary:\n";
    echo "Created tables: " . count($createdTables) . "/" . count($tables) . "\n";
    
    if (!empty($missingTables)) {
        echo "Missing tables: " . implode(', ', $missingTables) . "\n";
    }

    // Create sample data for testing
    if (count($createdTables) >= 8) { // Most tables created successfully
        echo "\nCreating sample data for testing...\n";
        
        try {
            // Create a sample research session
            $sampleSessionData = [
                'user_id' => 1, // Assuming user ID 1 exists
                'title' => 'AI Agent Army Market Research',
                'description' => 'Research into AI agent platforms, competitors, and market opportunities',
                'research_type' => 'market_analysis',
                'status' => 'active',
                'priority' => 'high',
                'tags' => json_encode(['ai', 'agents', 'market-research', 'competition']),
                'metadata' => json_encode(['created_by' => 'system', 'sample_data' => true]),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $sessionId = $db->insert(
                "INSERT INTO research_sessions (user_id, title, description, research_type, status, priority, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                array_values($sampleSessionData)
            );

            if ($sessionId) {
                echo "✓ Sample research session created (ID: $sessionId)\n";

                // Create sample links
                $sampleLinks = [
                    [
                        'title' => 'CrewAI - Multi-Agent Framework',
                        'url' => 'https://www.crewai.com/',
                        'description' => 'Leading multi-agent platform for business automation',
                        'link_type' => 'competitor',
                        'importance' => 'high'
                    ],
                    [
                        'title' => 'Microsoft AutoGen Documentation',
                        'url' => 'https://microsoft.github.io/autogen/',
                        'description' => 'Microsoft\'s conversational AI agent framework',
                        'link_type' => 'documentation',
                        'importance' => 'high'
                    ],
                    [
                        'title' => 'AI Agent Market Analysis 2024',
                        'url' => 'https://example.com/ai-agent-market-2024',
                        'description' => 'Comprehensive market analysis of AI agent platforms',
                        'link_type' => 'article',
                        'importance' => 'medium'
                    ]
                ];

                foreach ($sampleLinks as $linkData) {
                    $linkData['research_session_id'] = $sessionId;
                    $linkData['user_id'] = 1;
                    $linkData['status'] = 'to_review';
                    $linkData['notes'] = '';
                    $linkData['tags'] = json_encode([]);
                    $linkData['metadata'] = json_encode(['sample_data' => true]);
                    $linkData['created_at'] = date('Y-m-d H:i:s');
                    $linkData['updated_at'] = date('Y-m-d H:i:s');

                    $linkId = $db->insert(
                        "INSERT INTO research_links (research_session_id, user_id, title, url, description, link_type, importance, status, notes, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                        array_values($linkData)
                    );

                    if ($linkId) {
                        echo "✓ Sample link created: " . $linkData['title'] . "\n";
                    }
                }

                // Create sample plan
                $samplePlanData = [
                    'research_session_id' => $sessionId,
                    'user_id' => 1,
                    'title' => 'AI Agent Army Implementation Plan',
                    'description' => 'Comprehensive plan to implement competitive AI agent platform',
                    'plan_type' => 'implementation',
                    'priority' => 'high',
                    'estimated_effort' => 'large',
                    'estimated_duration_days' => 90,
                    'success_criteria' => 'Launch MVP with core features, acquire first 100 users',
                    'risks_and_mitigation' => 'Competition risk - mitigate with ADHD specialization',
                    'resources_needed' => 'Development team, AI model access, hosting infrastructure',
                    'status' => 'draft',
                    'progress' => 0,
                    'tags' => json_encode(['implementation', 'mvp', 'ai-agents']),
                    'metadata' => json_encode(['sample_data' => true]),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $planId = $db->insert(
                    "INSERT INTO research_plans (research_session_id, user_id, title, description, plan_type, priority, estimated_effort, estimated_duration_days, success_criteria, risks_and_mitigation, resources_needed, status, progress, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    array_values($samplePlanData)
                );

                if ($planId) {
                    echo "✓ Sample research plan created (ID: $planId)\n";
                }
            }

        } catch (Exception $e) {
            echo "✗ Error creating sample data: " . $e->getMessage() . "\n";
        }
    }

    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Research & Planning System Setup Complete!\n";
    echo str_repeat("=", 50) . "\n\n";

    echo "Next steps:\n";
    echo "1. Include the research routes in your main routing system\n";
    echo "2. Add navigation links to the research dashboard\n";
    echo "3. Test the system by creating a research session\n";
    echo "4. Customize the templates and workflows for your needs\n\n";

    echo "Access the research system at: /momentum/research\n\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>

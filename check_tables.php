<?php
require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    echo "Checking database tables...\n\n";
    
    $result = $db->fetchAll('SHOW TABLES');
    
    echo "All tables in database:\n";
    foreach($result as $table) {
        $tableName = $table[array_keys($table)[0]];
        echo "- $tableName\n";
    }
    
    echo "\nChecking research tables specifically:\n";
    $researchTables = [
        'research_sessions',
        'research_links', 
        'research_notes',
        'research_plans',
        'plan_action_items',
        'research_project_conversions'
    ];
    
    foreach ($researchTables as $table) {
        $exists = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
        if ($exists) {
            echo "✓ $table exists\n";

            // Count records
            try {
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM $table");
                echo "  Records: " . $count['count'] . "\n";
            } catch (Exception $e) {
                echo "  Error counting records: " . $e->getMessage() . "\n";
            }
        } else {
            echo "✗ $table missing\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>

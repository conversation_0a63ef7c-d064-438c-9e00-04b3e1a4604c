<?php
/**
 * Test Research & Planning System
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Change to the correct directory
chdir('C:/laragon/www/momentum');

// Include database connection
require_once 'src/utils/Database.php';
require_once 'src/models/ResearchSession.php';
require_once 'src/models/ResearchLink.php';
require_once 'src/models/ResearchPlan.php';

try {
    echo "Testing Research & Planning System...\n\n";

    // Test database connection
    $db = Database::getInstance();
    echo "✓ Database connection successful\n";

    // Test ResearchSession model
    $researchSessionModel = new ResearchSession();
    echo "✓ ResearchSession model loaded\n";

    // Test ResearchLink model
    $researchLinkModel = new ResearchLink();
    echo "✓ ResearchLink model loaded\n";

    // Test ResearchPlan model
    $researchPlanModel = new ResearchPlan();
    echo "✓ ResearchPlan model loaded\n";

    // Test table existence
    $tables = [
        'research_sessions',
        'research_links',
        'research_notes',
        'research_plans',
        'plan_action_items',
        'research_project_conversions'
    ];

    echo "\nChecking database tables:\n";
    foreach ($tables as $table) {
        $result = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
        if ($result) {
            echo "✓ Table '$table' exists\n";
        } else {
            echo "✗ Table '$table' missing\n";
        }
    }

    // Test sample data
    echo "\nChecking sample data:\n";
    $sessions = $researchSessionModel->getUserSessions(1);
    echo "✓ Found " . count($sessions) . " research sessions\n";

    if (!empty($sessions)) {
        $sessionId = $sessions[0]['id'];
        $links = $researchLinkModel->getSessionLinks($sessionId);
        echo "✓ Found " . count($links) . " links in first session\n";

        $plans = $researchPlanModel->getSessionPlans($sessionId);
        echo "✓ Found " . count($plans) . " plans in first session\n";
    }

    // Test statistics
    echo "\nTesting statistics:\n";
    $sessionStats = $researchSessionModel->getSessionStats(1);
    echo "✓ Session statistics: " . ($sessionStats['overall']['total_sessions'] ?? 0) . " total sessions\n";

    $linkStats = $researchLinkModel->getLinkStats(1);
    echo "✓ Link statistics: " . ($linkStats['overall']['total_links'] ?? 0) . " total links\n";

    $planStats = $researchPlanModel->getPlanStats(1);
    echo "✓ Plan statistics: " . ($planStats['overall']['total_plans'] ?? 0) . " total plans\n";

    // Test controller loading
    echo "\nTesting controller:\n";
    require_once 'src/controllers/ResearchPlanningController.php';
    $controller = new ResearchPlanningController();
    echo "✓ ResearchPlanningController loaded successfully\n";

    // Test route file
    echo "\nTesting route file:\n";
    if (file_exists('src/routes/research_routes.php')) {
        echo "✓ Research routes file exists\n";
    } else {
        echo "✗ Research routes file missing\n";
    }

    // Test view file
    echo "\nTesting view file:\n";
    if (file_exists('src/views/research/dashboard.php')) {
        echo "✓ Research dashboard view exists\n";
    } else {
        echo "✗ Research dashboard view missing\n";
    }

    // Test navigation integration
    echo "\nTesting navigation integration:\n";
    $navContent = file_get_contents('src/views/partials/two-tier-navigation.php');
    if (strpos($navContent, '/momentum/research') !== false) {
        echo "✓ Research link added to navigation\n";
    } else {
        echo "✗ Research link missing from navigation\n";
    }

    // Test main index.php integration
    echo "\nTesting main index.php integration:\n";
    $indexContent = file_get_contents('public/index.php');
    if (strpos($indexContent, 'ResearchPlanningController') !== false) {
        echo "✓ ResearchPlanningController included in main index\n";
    } else {
        echo "✗ ResearchPlanningController missing from main index\n";
    }

    if (strpos($indexContent, 'research_routes.php') !== false) {
        echo "✓ Research routes included in main index\n";
    } else {
        echo "✗ Research routes missing from main index\n";
    }

    echo "\n" . str_repeat("=", 60) . "\n";
    echo "RESEARCH & PLANNING SYSTEM TEST COMPLETE!\n";
    echo str_repeat("=", 60) . "\n\n";

    echo "✅ System Status: READY\n";
    echo "✅ Database: Connected and populated\n";
    echo "✅ Models: All loaded successfully\n";
    echo "✅ Controller: Ready for requests\n";
    echo "✅ Routes: Integrated with main system\n";
    echo "✅ Navigation: Research link added\n\n";

    echo "🚀 You can now access the Research & Planning system at:\n";
    echo "   http://localhost/momentum/research\n\n";

    echo "📋 Quick Start:\n";
    echo "1. Click 'Research' in the main navigation\n";
    echo "2. Create your first research session\n";
    echo "3. Add links and take notes\n";
    echo "4. Create action plans\n";
    echo "5. Convert plans to projects\n\n";

    echo "🧠 ADHD-Friendly Features:\n";
    echo "- Focus sessions with timers\n";
    echo "- Visual progress tracking\n";
    echo "- Chunked information display\n";
    echo "- Easy context switching\n";
    echo "- Auto-save functionality\n\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
